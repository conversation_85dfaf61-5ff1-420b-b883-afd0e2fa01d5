<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Calendar Picker Test</title>
    <!-- Bootstrap 3.3.7 CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">
    <!-- jQuery UI CSS -->
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css"
          integrity="sha256-fECPsHzFOcZLdG/y4+W8y0fyKn/KYAJdtLyV8UaWb0g="
          crossorigin="anonymous">
    <style>
        /* Date picker styling */
        .datepicker-trigger {
            background-color: #f5f5f5;
            border-left: 1px solid #ccc;
            padding: 5px 8px;
            cursor: pointer;
        }

        .datepicker-trigger:hover {
            background-color: #e8e8e8;
        }

        .datepicker-trigger .glyphicon-calendar {
            color: #337ab7;
            font-size: 12px;
        }

        /* Ensure input group sizing is consistent */
        .input-group-sm .input-group-addon {
            padding: 5px 8px;
            font-size: 12px;
            line-height: 1.5;
            border-radius: 3px;
        }

        /* Custom datepicker styling to match the expected result */
        .ui-datepicker {
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            font-size: 11px;
            width: 200px;
            border: 1px solid #999;
            background: white;
            box-shadow: 2px 2px 5px rgba(0,0,0,0.3);
            padding: 0;
        }

        .ui-datepicker .ui-datepicker-header {
            background: #f0f0f0;
            border: none;
            border-bottom: 1px solid #ccc;
            color: #333;
            font-weight: normal;
            padding: 4px 8px;
            position: relative;
            height: 20px;
        }

        .ui-datepicker .ui-datepicker-prev,
        .ui-datepicker .ui-datepicker-next {
            position: absolute;
            top: 2px;
            width: 16px;
            height: 16px;
            background: #f0f0f0;
            border: 1px solid #999;
            cursor: pointer;
            text-align: center;
            line-height: 14px;
            font-size: 10px;
            color: #333;
        }

        .ui-datepicker .ui-datepicker-prev {
            left: 4px;
        }

        .ui-datepicker .ui-datepicker-next {
            right: 4px;
        }

        .ui-datepicker .ui-datepicker-prev:hover,
        .ui-datepicker .ui-datepicker-next:hover {
            background: #e0e0e0;
        }

        .ui-datepicker .ui-datepicker-title {
            line-height: 20px;
            margin: 0 25px;
            text-align: center;
            font-weight: bold;
            font-size: 11px;
        }

        .ui-datepicker table {
            width: 100%;
            font-size: 11px;
            border-collapse: collapse;
            margin: 0;
            border-spacing: 0;
        }

        .ui-datepicker th {
            padding: 4px 2px;
            text-align: center;
            font-weight: normal;
            border: none;
            background: #f8f8f8;
            color: #666;
            font-size: 10px;
            border-bottom: 1px solid #ddd;
        }

        .ui-datepicker td {
            border: none;
            padding: 0;
            text-align: center;
        }

        .ui-datepicker td span,
        .ui-datepicker td a {
            display: block;
            padding: 2px;
            text-align: center;
            text-decoration: none;
            border: 1px solid transparent;
            font-size: 11px;
            color: #333;
            width: 20px;
            height: 16px;
            line-height: 16px;
            margin: 1px;
        }

        .ui-datepicker td a:hover {
            background: #316AC5;
            color: white;
            border: 1px solid #316AC5;
        }

        .ui-datepicker .ui-datepicker-today a {
            background: #316AC5;
            color: white;
            font-weight: bold;
            border: 1px solid #316AC5;
        }

        .ui-datepicker .ui-datepicker-current-day a {
            background: #316AC5;
            color: white;
            font-weight: bold;
            border: 1px solid #316AC5;
        }

        .ui-datepicker .ui-datepicker-other-month a {
            color: #ccc;
        }

        /* Hide the default jQuery UI icons and use custom arrows */
        .ui-datepicker .ui-icon {
            display: none;
        }

        .ui-datepicker .ui-datepicker-prev:before {
            content: "◀";
            font-size: 8px;
        }

        .ui-datepicker .ui-datepicker-next:before {
            content: "▶";
            font-size: 8px;
        }

        .container {
            margin-top: 50px;
        }

        .test-table {
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Calendar Picker Test</h1>
        <p>This page tests the calendar picker functionality similar to the MOSCI screen.</p>

        <div class="panel panel-primary">
            <div class="panel-heading">
                Test Calendar Picker
            </div>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-condensed test-table">
                        <thead>
                            <tr>
                                <th style="width:120px;">Offender #</th>
                                <th style="width:150px;">Last Name</th>
                                <th style="width:150px;">First Name</th>
                                <th style="width:140px;">Scheduled Date</th>
                                <th style="width:200px;">Comments</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="text" class="form-control input-sm" value="A123456" /></td>
                                <td><input type="text" class="form-control input-sm" value="SMITH" /></td>
                                <td><input type="text" class="form-control input-sm" value="JOHN" /></td>
                                <td>
                                    <div class="input-group input-group-sm">
                                        <input type="text" class="form-control input-sm datepicker-input" value="06/15/2025" />
                                        <span class="input-group-addon datepicker-trigger">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </td>
                                <td><input type="text" class="form-control input-sm" value="Test comment" /></td>
                            </tr>
                            <tr>
                                <td><input type="text" class="form-control input-sm" value="R789012" /></td>
                                <td><input type="text" class="form-control input-sm" value="DOE" /></td>
                                <td><input type="text" class="form-control input-sm" value="JANE" /></td>
                                <td>
                                    <div class="input-group input-group-sm">
                                        <input type="text" class="form-control input-sm datepicker-input" value="" />
                                        <span class="input-group-addon datepicker-trigger">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </td>
                                <td><input type="text" class="form-control input-sm" value="" /></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <button type="button" class="btn btn-primary" id="addRowBtn">
                    <span class="glyphicon glyphicon-plus"></span> Add New Row
                </button>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js" integrity="sha384-Tc5IQib027qvyjSMfHjOMaLkfuWVxZxUPnCJA7l2mCWNIpG9mGCD8wGNIcPD7Txa" crossorigin="anonymous"></script>
    <!-- jQuery UI -->
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"
            integrity="sha256-lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0="
            crossorigin="anonymous"></script>

    <script>
        $(function() {
            // Initialize datepickers
            function initializeDatepickers() {
                $('.datepicker-input').each(function() {
                    var $input = $(this);
                    if (!$input.hasClass('hasDatepicker')) {
                        $input.datepicker({
                            dateFormat: 'mm/dd/yy',
                            changeMonth: false,
                            changeYear: false,
                            showButtonPanel: false,
                            yearRange: '-10:+10',
                            showOtherMonths: true,
                            selectOtherMonths: false,
                            firstDay: 0 // Sunday
                        });
                    }
                });
            }

            // Handle calendar icon clicks with event delegation
            $(document).on('click', '.datepicker-trigger', function(e) {
                e.preventDefault();
                e.stopPropagation();
                var $input = $(this).siblings('.datepicker-input');
                console.log('Calendar icon clicked, input found:', $input.length);

                if ($input.length > 0) {
                    if ($input.hasClass('hasDatepicker')) {
                        if ($input.datepicker('widget').is(':visible')) {
                            $input.datepicker('hide');
                        } else {
                            $input.datepicker('show');
                        }
                    } else {
                        // Initialize datepicker if not already done
                        $input.datepicker({
                            dateFormat: 'mm/dd/yy',
                            changeMonth: false,
                            changeYear: false,
                            showButtonPanel: false,
                            yearRange: '-10:+10',
                            showOtherMonths: true,
                            selectOtherMonths: false,
                            firstDay: 0 // Sunday
                        });
                        $input.datepicker('show');
                    }
                }
            });

            // Initialize datepickers on page load
            initializeDatepickers();

            // Add new row functionality
            $('#addRowBtn').on('click', function() {
                var newRow = `
                    <tr>
                        <td><input type="text" class="form-control input-sm" value="" /></td>
                        <td><input type="text" class="form-control input-sm" value="" /></td>
                        <td><input type="text" class="form-control input-sm" value="" /></td>
                        <td>
                            <div class="input-group input-group-sm">
                                <input type="text" class="form-control input-sm datepicker-input" value="" />
                                <span class="input-group-addon datepicker-trigger">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </td>
                        <td><input type="text" class="form-control input-sm" value="" /></td>
                    </tr>
                `;
                
                $('.test-table tbody').append(newRow);

                // Initialize datepicker for the new row
                $('.test-table tbody tr:last-child .datepicker-input').each(function() {
                    var $input = $(this);
                    if (!$input.hasClass('hasDatepicker')) {
                        $input.datepicker({
                            dateFormat: 'mm/dd/yy',
                            changeMonth: false,
                            changeYear: false,
                            showButtonPanel: false,
                            yearRange: '-10:+10',
                            showOtherMonths: true,
                            selectOtherMonths: false,
                            firstDay: 0 // Sunday
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
