<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Calendar Picker Test</title>
    <!-- Bootstrap 3.3.7 CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">
    <!-- jQuery UI CSS -->
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css"
          integrity="sha256-fECPsHzFOcZLdG/y4+W8y0fyKn/KYAJdtLyV8UaWb0g="
          crossorigin="anonymous">
    <style>
        /* Date picker styling */
        .datepicker-trigger {
            background-color: #f5f5f5;
            border-left: 1px solid #ccc;
            padding: 5px 8px;
            cursor: pointer;
        }

        .datepicker-trigger:hover {
            background-color: #e8e8e8;
        }

        .datepicker-trigger .glyphicon-calendar {
            color: #337ab7;
            font-size: 12px;
        }

        /* Ensure input group sizing is consistent */
        .input-group-sm .input-group-addon {
            padding: 5px 8px;
            font-size: 12px;
            line-height: 1.5;
            border-radius: 3px;
        }

        /* Custom datepicker styling to match the screenshot */
        .ui-datepicker {
            font-size: 12px;
            width: 200px;
        }

        .ui-datepicker .ui-datepicker-header {
            background: #f5f5f5;
            border: 1px solid #ddd;
            color: #333;
            font-weight: bold;
        }

        .ui-datepicker .ui-datepicker-title {
            line-height: 1.8em;
            margin: 0 2.3em;
            text-align: center;
            font-weight: bold;
        }

        .ui-datepicker table {
            width: 100%;
            font-size: 11px;
            border-collapse: collapse;
            margin: 0 0 .4em;
        }

        .ui-datepicker th {
            padding: .7em .3em;
            text-align: center;
            font-weight: bold;
            border: 0;
            background: #f5f5f5;
        }

        .ui-datepicker td {
            border: 0;
            padding: 1px;
        }

        .ui-datepicker td span, .ui-datepicker td a {
            display: block;
            padding: .2em;
            text-align: right;
            text-decoration: none;
            border: 1px solid transparent;
        }

        .ui-datepicker td a:hover {
            background: #337ab7;
            color: white;
        }

        .ui-datepicker .ui-datepicker-today a {
            background: #337ab7;
            color: white;
            font-weight: bold;
        }

        .container {
            margin-top: 50px;
        }

        .test-table {
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Calendar Picker Test</h1>
        <p>This page tests the calendar picker functionality similar to the MOSCI screen.</p>

        <div class="panel panel-primary">
            <div class="panel-heading">
                Test Calendar Picker
            </div>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-condensed test-table">
                        <thead>
                            <tr>
                                <th style="width:120px;">Offender #</th>
                                <th style="width:150px;">Last Name</th>
                                <th style="width:150px;">First Name</th>
                                <th style="width:140px;">Scheduled Date</th>
                                <th style="width:200px;">Comments</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="text" class="form-control input-sm" value="A123456" /></td>
                                <td><input type="text" class="form-control input-sm" value="SMITH" /></td>
                                <td><input type="text" class="form-control input-sm" value="JOHN" /></td>
                                <td>
                                    <div class="input-group input-group-sm">
                                        <input type="text" class="form-control input-sm datepicker-input" value="06/15/2025" />
                                        <span class="input-group-addon datepicker-trigger">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </td>
                                <td><input type="text" class="form-control input-sm" value="Test comment" /></td>
                            </tr>
                            <tr>
                                <td><input type="text" class="form-control input-sm" value="R789012" /></td>
                                <td><input type="text" class="form-control input-sm" value="DOE" /></td>
                                <td><input type="text" class="form-control input-sm" value="JANE" /></td>
                                <td>
                                    <div class="input-group input-group-sm">
                                        <input type="text" class="form-control input-sm datepicker-input" value="" />
                                        <span class="input-group-addon datepicker-trigger">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </td>
                                <td><input type="text" class="form-control input-sm" value="" /></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <button type="button" class="btn btn-primary" id="addRowBtn">
                    <span class="glyphicon glyphicon-plus"></span> Add New Row
                </button>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js" integrity="sha384-Tc5IQib027qvyjSMfHjOMaLkfuWVxZxUPnCJA7l2mCWNIpG9mGCD8wGNIcPD7Txa" crossorigin="anonymous"></script>
    <!-- jQuery UI -->
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"
            integrity="sha256-lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0="
            crossorigin="anonymous"></script>

    <script>
        $(function() {
            // Initialize datepickers
            function initializeDatepickers() {
                $('.datepicker-input').each(function() {
                    var $input = $(this);
                    if (!$input.hasClass('hasDatepicker')) {
                        $input.datepicker({
                            dateFormat: 'mm/dd/yy',
                            changeMonth: true,
                            changeYear: true,
                            showButtonPanel: true,
                            yearRange: '-10:+10'
                        });
                    }
                });
            }

            // Handle calendar icon clicks with event delegation
            $(document).on('click', '.datepicker-trigger', function(e) {
                e.preventDefault();
                e.stopPropagation();
                var $input = $(this).siblings('.datepicker-input');
                console.log('Calendar icon clicked, input found:', $input.length);

                if ($input.length > 0) {
                    if ($input.hasClass('hasDatepicker')) {
                        if ($input.datepicker('widget').is(':visible')) {
                            $input.datepicker('hide');
                        } else {
                            $input.datepicker('show');
                        }
                    } else {
                        // Initialize datepicker if not already done
                        $input.datepicker({
                            dateFormat: 'mm/dd/yy',
                            changeMonth: true,
                            changeYear: true,
                            showButtonPanel: true,
                            yearRange: '-10:+10'
                        });
                        $input.datepicker('show');
                    }
                }
            });

            // Initialize datepickers on page load
            initializeDatepickers();

            // Add new row functionality
            $('#addRowBtn').on('click', function() {
                var newRow = `
                    <tr>
                        <td><input type="text" class="form-control input-sm" value="" /></td>
                        <td><input type="text" class="form-control input-sm" value="" /></td>
                        <td><input type="text" class="form-control input-sm" value="" /></td>
                        <td>
                            <div class="input-group input-group-sm">
                                <input type="text" class="form-control input-sm datepicker-input" value="" />
                                <span class="input-group-addon datepicker-trigger">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </td>
                        <td><input type="text" class="form-control input-sm" value="" /></td>
                    </tr>
                `;
                
                $('.test-table tbody').append(newRow);

                // Initialize datepicker for the new row
                $('.test-table tbody tr:last-child .datepicker-input').each(function() {
                    var $input = $(this);
                    if (!$input.hasClass('hasDatepicker')) {
                        $input.datepicker({
                            dateFormat: 'mm/dd/yy',
                            changeMonth: true,
                            changeYear: true,
                            showButtonPanel: true,
                            yearRange: '-10:+10'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
