/* Bootstrap 3.3.7 Customizations */
body {
  padding-top: 20px;
  padding-bottom: 60px;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.42857143;
  color: #333;
  background-color: #fff;
}

/* Footer styles */
.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 60px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  padding-top: 20px;
}

/* Panel customizations */
.panel-primary > .panel-heading {
  background-color: #337ab7;
  border-color: #337ab7;
  color: white;
}

/* Table styles */
.table-condensed > thead > tr > th,
.table-condensed > tbody > tr > td {
  padding: 5px;
  vertical-align: middle;
}

.table-bordered {
  border: 1px solid #ddd;
}

/* Form control sizes for Bootstrap 3 */
.input-sm {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}

/* Button spacing */
.btn {
  margin-right: 5px;
}

/* MOSCI specific styles */
.no-print {
  margin-bottom: 20px;
}

#inmateTable th {
  background-color: #f5f5f5;
  font-weight: bold;
  text-align: center;
}

#inmateTable input[type="checkbox"] {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .text-right {
    text-align: left;
    margin-top: 10px;
  }
}

/* Spinning icon for loading indicators */
.glyphicon-spin {
  animation: spin 1s infinite linear;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Date picker styling */
.datepicker-trigger {
  background-color: #f5f5f5;
  border-left: 1px solid #ccc;
  padding: 5px 8px;
}

.datepicker-trigger:hover {
  background-color: #e8e8e8;
}

.datepicker-trigger .glyphicon-calendar {
  color: #337ab7;
  font-size: 12px;
}

/* Ensure input group sizing is consistent */
.input-group-sm .input-group-addon {
  padding: 5px 8px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}

/* Custom datepicker styling to match the screenshot */
.ui-datepicker {
  font-size: 12px;
  width: 200px;
}

.ui-datepicker .ui-datepicker-header {
  background: #f5f5f5;
  border: 1px solid #ddd;
  color: #333;
  font-weight: bold;
}

.ui-datepicker .ui-datepicker-title {
  line-height: 1.8em;
  margin: 0 2.3em;
  text-align: center;
  font-weight: bold;
}

.ui-datepicker table {
  width: 100%;
  font-size: 11px;
  border-collapse: collapse;
  margin: 0 0 .4em;
}

.ui-datepicker th {
  padding: .7em .3em;
  text-align: center;
  font-weight: bold;
  border: 0;
  background: #f5f5f5;
}

.ui-datepicker td {
  border: 0;
  padding: 1px;
}

.ui-datepicker td span, .ui-datepicker td a {
  display: block;
  padding: .2em;
  text-align: right;
  text-decoration: none;
  border: 1px solid transparent;
}

.ui-datepicker td a:hover {
  background: #337ab7;
  color: white;
}

.ui-datepicker .ui-datepicker-today a {
  background: #337ab7;
  color: white;
  font-weight: bold;
}
